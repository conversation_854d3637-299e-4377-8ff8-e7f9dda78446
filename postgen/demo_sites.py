#!/usr/bin/env python3
"""
WordPress 站点管理器演示脚本
展示主要功能的使用方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sites import (
    get_site_categories,
    find_category_in_sites, 
    publish_article,
    publish_to_specific_site,
    print_cache_status,
    print_all_categories,
    clear_cache
)

def demo_basic_usage():
    """演示基本用法"""
    print("🎯 演示1: 基本功能")
    print("=" * 50)
    
    # 1. 获取主站点分类
    print("\n📂 获取主站点分类:")
    categories = get_site_categories('www')
    print(f"主站点共有 {len(categories)} 个分类")
    
    # 显示前几个分类
    for i, (name, cat_id) in enumerate(list(categories.items())[:3]):
        print(f"  {i+1}. {name} (ID: {cat_id})")
    
    # 2. 查找特定分类
    print("\n🔍 查找'生肖今日运势'分类:")
    results = find_category_in_sites('生肖今日运势')
    
    if results:
        print(f"找到 {len(results)} 个站点包含该分类:")
        for site_key, site_url, cat_id in results:
            print(f"  - {site_key}: {site_url} (ID: {cat_id})")
    else:
        print("  未找到包含该分类的站点")

def demo_cache_management():
    """演示缓存管理"""
    print("\n🎯 演示2: 缓存管理")
    print("=" * 50)
    
    # 显示缓存状态
    print("\n📊 当前缓存状态:")
    print_cache_status()
    
    # 演示强制刷新
    print("\n🔄 演示强制刷新缓存:")
    print("正在强制刷新主站点分类...")
    categories = get_site_categories('www', force_refresh=True)
    print(f"刷新后获得 {len(categories)} 个分类")

def demo_publish_simulation():
    """演示发布功能（模拟）"""
    print("\n🎯 演示3: 发布功能（模拟）")
    print("=" * 50)
    
    # 准备测试数据
    test_title = "今日生肖运势 - 鼠年大吉"
    test_content = """
今日鼠年运势分析：

**财运**: ⭐⭐⭐⭐
今日财运不错，适合投资理财。

**事业**: ⭐⭐⭐
工作中可能遇到新机会，要把握住。

**感情**: ⭐⭐⭐⭐⭐
感情运势极佳，单身者有望脱单。

**健康**: ⭐⭐⭐
注意休息，避免过度劳累。

愿您今日一切顺利！
    """.strip()
    
    test_category = "生肖今日运势"
    
    print(f"📝 模拟发布文章:")
    print(f"标题: {test_title}")
    print(f"分类: {test_category}")
    print(f"内容长度: {len(test_content)} 字符")
    
    # 查找目标站点
    print(f"\n🎯 查找包含分类 '{test_category}' 的站点:")
    results = find_category_in_sites(test_category)
    
    if results:
        print(f"找到 {len(results)} 个目标站点:")
        for site_key, site_url, cat_id in results:
            print(f"  ✅ {site_key}: {site_url} (分类ID: {cat_id})")
        
        print(f"\n💡 如果执行 publish_article() 函数，文章将会发布到以上 {len(results)} 个站点")
        print("💡 如果只想发布到特定站点，可以使用 publish_to_specific_site() 函数")
        
        # 演示代码（不实际执行）
        print(f"\n📋 示例代码:")
        print("# 发布到所有包含该分类的站点")
        print("results = publish_article(")
        print(f"    title='{test_title}',")
        print(f"    content=content,")
        print(f"    category_name='{test_category}'")
        print(")")
        print()
        print("# 或者只发布到主站点")
        print("result = publish_to_specific_site(")
        print("    site_key='www',")
        print(f"    title='{test_title}',")
        print("    content=content,")
        print(f"    category_name='{test_category}'")
        print(")")
        
    else:
        print("❌ 没有找到包含该分类的站点")

def demo_advanced_usage():
    """演示高级用法"""
    print("\n🎯 演示4: 高级用法")
    print("=" * 50)
    
    # 1. 批量查找多个分类
    print("\n📋 批量查找多个分类:")
    categories_to_find = ["生肖今日运势", "周易文化", "命理健康"]
    
    for category in categories_to_find:
        results = find_category_in_sites(category)
        print(f"  '{category}': 存在于 {len(results)} 个站点")
    
    # 2. 统计所有站点的分类分布
    print("\n📊 分析所有站点的分类分布:")
    
    # 这里我们只演示概念，不实际执行耗时操作
    print("  💡 可以使用 get_all_sites_categories() 获取所有站点分类")
    print("  💡 然后统计每个分类在多少个站点中存在")
    print("  💡 找出最常见的分类和独有的分类")
    
    # 3. 演示错误处理
    print("\n⚠️ 错误处理演示:")
    try:
        # 尝试获取不存在的站点
        categories = get_site_categories('nonexistent_site')
    except ValueError as e:
        print(f"  ✅ 正确捕获错误: {e}")

def main():
    """主演示函数"""
    print("🚀 WordPress 站点管理器功能演示")
    print("=" * 60)
    print("本演示将展示站点管理器的主要功能")
    print("包括分类查询、缓存管理、发布模拟等")
    print()
    
    try:
        # 基本功能演示
        demo_basic_usage()
        
        # 缓存管理演示
        demo_cache_management()
        
        # 发布功能演示
        demo_publish_simulation()
        
        # 高级用法演示
        demo_advanced_usage()
        
        print("\n🎉 演示完成!")
        print("\n📚 更多用法请参考:")
        print("  - sites_usage_examples.md: 详细使用指南")
        print("  - test_sites.py: 交互式测试工具")
        print("  - sites.py: 源代码和注释")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查网络连接和WordPress站点状态")

if __name__ == "__main__":
    main()
