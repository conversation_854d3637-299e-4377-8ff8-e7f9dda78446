#!/usr/bin/env python3
"""
配置管理工具
用于查看和修改系统配置
"""

import json
from config import get_config

def show_config():
    """显示当前配置"""
    config = get_config()
    
    print("🔧 当前系统配置")
    print("=" * 60)
    
    print("\n📡 OpenAI API 配置:")
    openai_config = config.get_openai_config()
    print(f"  Base URL: {openai_config['base_url']}")
    print(f"  API Key: {openai_config['api_key'][:20]}...")
    print(f"  模型: {openai_config['model']}")
    print(f"  温度: {openai_config['temperature']}")
    print(f"  最大Token: {openai_config['max_tokens']}")
    print(f"  超时时间: {openai_config['timeout']}秒")
    
    print("\n🌐 WordPress 配置:")
    wp_config = config.get_wordpress_config()
    print(f"  用户名: {wp_config['username']}")
    print(f"  API Key: {wp_config['api_key'][:20]}...")
    print(f"  分类名称: {wp_config['category_name']}")
    print(f"  SSL验证: {wp_config['verify_ssl']}")
    print(f"  超时时间: {wp_config['timeout']}秒")
    
    print("\n🏢 WordPress 站点:")
    sites = config.get_wordpress_sites()
    for key, url in sites.items():
        print(f"  {key}: {url}")
    
    print("\n🐲 生肖配置:")
    zodiac_config = config.get_zodiac_config()
    print(f"  生肖列表: {', '.join(zodiac_config['animals'])}")
    print(f"  批量延迟: {zodiac_config['batch_delay']}秒")
    print(f"  重试次数: {zodiac_config['retry_count']}")
    print(f"  重试延迟: {zodiac_config['retry_delay']}秒")
    
    print("\n⏰ 调度器配置:")
    scheduler_config = config.get_scheduler_config()
    print(f"  生肖运势时间: {scheduler_config['zodiac_fortune_time']}")
    print(f"  日志文件: {scheduler_config['log_file']}")
    print(f"  错误日志: {scheduler_config['error_log_file']}")
    print(f"  任务超时: {scheduler_config['task_timeout']}秒")

    print("\n🔄 重试配置:")
    retry_config = config.get_retry_config()
    print(f"  OpenAI重试: {retry_config['openai_max_attempts']}次, 延迟{retry_config['openai_delay']}秒")
    print(f"  WordPress重试: {retry_config['wordpress_max_attempts']}次, 延迟{retry_config['wordpress_delay']}秒")
    print(f"  网络重试: {retry_config['network_max_attempts']}次, 延迟{retry_config['network_delay']}秒")
    print(f"  退避倍数: {retry_config['backoff_multiplier']}")

def test_openai_connection():
    """测试OpenAI连接"""
    print("\n🧪 测试OpenAI连接...")
    
    try:
        from config import get_openai_client
        client = get_openai_client()
        
        # 简单测试
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "测试连接，请回复'连接成功'"}],
            max_tokens=10
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ OpenAI连接成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI连接失败: {e}")
        return False

def test_wordpress_connection():
    """测试WordPress连接"""
    print("\n🧪 测试WordPress连接...")
    
    try:
        import requests
        config = get_config()
        
        # 测试主站点
        www_site = config.get_wordpress_site_url('www')
        test_url = f"{www_site}/wp-json/wp/v2/categories"
        
        wp_config = config.get_wordpress_config()
        response = requests.get(
            test_url,
            verify=wp_config["verify_ssl"],
            timeout=wp_config["timeout"]
        )
        
        if response.status_code == 200:
            categories = response.json()
            print(f"✅ WordPress连接成功，找到 {len(categories)} 个分类")
            return True
        else:
            print(f"❌ WordPress连接失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ WordPress连接失败: {e}")
        return False

def update_schedule_time():
    """更新调度时间"""
    config = get_config()
    current_time = config.get_scheduler_config()["zodiac_fortune_time"]
    
    print(f"\n⏰ 当前生肖运势生成时间: {current_time}")
    new_time = input("请输入新的时间 (格式: HH:MM，回车跳过): ").strip()
    
    if new_time:
        try:
            # 验证时间格式
            hour, minute = new_time.split(':')
            hour = int(hour)
            minute = int(minute)
            
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                config.update_scheduler_time("zodiac_fortune", new_time)
                print(f"✅ 调度时间已更新为: {new_time}")
            else:
                print("❌ 时间格式错误，请使用 HH:MM 格式")
        except ValueError:
            print("❌ 时间格式错误，请使用 HH:MM 格式")

def add_wordpress_site():
    """添加WordPress站点"""
    config = get_config()
    
    print("\n🏢 添加新的WordPress站点")
    key = input("请输入站点标识 (如: new_site): ").strip()
    url = input("请输入站点URL (如: https://example.com): ").strip()
    
    if key and url:
        config.add_wordpress_site(key, url)
        print(f"✅ 已添加站点: {key} -> {url}")
    else:
        print("❌ 站点标识和URL不能为空")

def export_config():
    """导出配置到JSON文件"""
    config = get_config()
    
    config_data = {
        "openai": config.get_openai_config(),
        "wordpress": config.get_wordpress_config(),
        "wordpress_sites": config.get_wordpress_sites(),
        "zodiac": config.get_zodiac_config(),
        "scheduler": config.get_scheduler_config()
    }
    
    filename = "config_backup.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 配置已导出到: {filename}")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("🔧 配置管理工具")
        print("=" * 60)
        print("1. 查看当前配置")
        print("2. 测试OpenAI连接")
        print("3. 测试WordPress连接")
        print("4. 更新调度时间")
        print("5. 添加WordPress站点")
        print("6. 导出配置")
        print("0. 退出")
        
        choice = input("\n请选择 (0-6): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            show_config()
        elif choice == '2':
            test_openai_connection()
        elif choice == '3':
            test_wordpress_connection()
        elif choice == '4':
            update_schedule_time()
        elif choice == '5':
            add_wordpress_site()
        elif choice == '6':
            export_config()
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
