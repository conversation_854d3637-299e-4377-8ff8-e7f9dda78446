#!/usr/bin/env python3
"""
测试 sites.py 的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sites import (
    site_manager, 
    get_site_categories, 
    find_category_in_sites, 
    publish_article,
    publish_to_specific_site,
    print_cache_status,
    print_all_categories,
    clear_cache
)

def test_basic_functions():
    """测试基本功能"""
    print("🧪 测试基本功能")
    print("=" * 50)
    
    try:
        # 1. 测试获取站点分类
        print("\n1. 测试获取主站点分类...")
        categories = get_site_categories('www')
        print(f"✅ 主站点分类数量: {len(categories)}")
        if categories:
            print("   前5个分类:")
            for i, (name, cat_id) in enumerate(list(categories.items())[:5]):
                print(f"   - {name} (ID: {cat_id})")
        
        # 2. 测试查找分类
        print("\n2. 测试查找分类...")
        test_category = "生肖今日运势"
        results = find_category_in_sites(test_category)
        print(f"✅ 找到 {len(results)} 个站点包含分类 '{test_category}'")
        for site_key, site_url, cat_id in results:
            print(f"   - {site_key}: {site_url} (ID: {cat_id})")
        
        # 3. 测试缓存状态
        print("\n3. 缓存状态:")
        print_cache_status()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_publish_simulation():
    """模拟发布测试（不实际发布）"""
    print("\n🧪 模拟发布测试")
    print("=" * 50)
    
    # 测试数据
    test_title = "测试文章标题"
    test_content = "这是一篇测试文章的内容。"
    test_category = "生肖今日运势"
    
    try:
        # 查找包含该分类的站点
        results = find_category_in_sites(test_category)
        
        if results:
            print(f"✅ 找到 {len(results)} 个站点包含分类 '{test_category}'")
            print("如果执行发布，将会发布到以下站点:")
            for site_key, site_url, cat_id in results:
                print(f"   - {site_key}: {site_url} (分类ID: {cat_id})")
        else:
            print(f"⚠️ 没有找到包含分类 '{test_category}' 的站点")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def interactive_test():
    """交互式测试"""
    print("\n🎮 交互式测试")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看所有站点分类")
        print("2. 查找特定分类")
        print("3. 查看缓存状态")
        print("4. 清除缓存")
        print("5. 模拟发布文章")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 退出测试")
            break
        elif choice == "1":
            print_all_categories()
        elif choice == "2":
            category_name = input("请输入要查找的分类名称: ").strip()
            if category_name:
                results = find_category_in_sites(category_name)
                if results:
                    print(f"✅ 找到 {len(results)} 个站点包含分类 '{category_name}':")
                    for site_key, site_url, cat_id in results:
                        print(f"   - {site_key}: {site_url} (ID: {cat_id})")
                else:
                    print(f"❌ 没有找到包含分类 '{category_name}' 的站点")
        elif choice == "3":
            print_cache_status()
        elif choice == "4":
            clear_cache()
            print("✅ 缓存已清除")
        elif choice == "5":
            title = input("请输入文章标题: ").strip()
            content = input("请输入文章内容: ").strip()
            category = input("请输入分类名称: ").strip()
            
            if title and content and category:
                print(f"\n模拟发布文章:")
                print(f"标题: {title}")
                print(f"内容: {content[:50]}...")
                print(f"分类: {category}")
                
                results = find_category_in_sites(category)
                if results:
                    print(f"\n将会发布到 {len(results)} 个站点:")
                    for site_key, site_url, cat_id in results:
                        print(f"   - {site_key}: {site_url}")
                else:
                    print(f"❌ 没有找到包含分类 '{category}' 的站点")
            else:
                print("❌ 请填写完整信息")
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🚀 WordPress站点管理器测试工具")
    print("=" * 60)
    
    # 基本功能测试
    if test_basic_functions():
        print("\n✅ 基本功能测试通过")
    else:
        print("\n❌ 基本功能测试失败")
        return
    
    # 模拟发布测试
    if test_publish_simulation():
        print("\n✅ 模拟发布测试通过")
    else:
        print("\n❌ 模拟发布测试失败")
    
    # 询问是否进行交互式测试
    choice = input("\n是否进行交互式测试? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        interactive_test()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
