# WordPress 站点管理器使用指南

## 概述

`sites.py` 提供了一个强大的 WordPress 站点管理器，支持：
- 🗂️ **分类缓存机制** - 自动缓存站点分类信息，提高查询速度
- 🔍 **一键查找分类** - 在所有站点中快速查找指定分类
- 📝 **一键发布文章** - 自动找到对应分类的站点并发布文章
- 🔄 **重试机制** - 内置网络重试，提高稳定性

## 主要功能

### 1. 获取站点分类

```python
from sites import get_site_categories

# 获取主站点的所有分类
categories = get_site_categories('www')
print(f"分类数量: {len(categories)}")

# 强制刷新缓存
categories = get_site_categories('www', force_refresh=True)
```

### 2. 查找分类在哪些站点

```python
from sites import find_category_in_sites

# 查找"生肖今日运势"分类在哪些站点
results = find_category_in_sites('生肖今日运势')

for site_key, site_url, category_id in results:
    print(f"站点: {site_key}, URL: {site_url}, 分类ID: {category_id}")
```

### 3. 一键发布文章

```python
from sites import publish_article

# 发布到所有包含该分类的站点
results = publish_article(
    title="今日生肖运势",
    content="文章内容...",
    category_name="生肖今日运势"
)

# 检查发布结果
for site_key, result in results.items():
    if result['success']:
        print(f"✅ {site_key}: 发布成功 - {result['post_url']}")
    else:
        print(f"❌ {site_key}: 发布失败 - {result['error']}")
```

### 4. 发布到指定站点

```python
from sites import publish_to_specific_site

# 只发布到指定站点
result = publish_to_specific_site(
    site_key='www',
    title="今日生肖运势", 
    content="文章内容...",
    category_name="生肖今日运势"
)

if result['success']:
    print(f"发布成功: {result['post_url']}")
else:
    print(f"发布失败: {result['error']}")
```

### 5. 发布到指定的多个站点

```python
from sites import publish_article

# 只发布到指定的站点列表
results = publish_article(
    title="今日生肖运势",
    content="文章内容...", 
    category_name="生肖今日运势",
    target_sites=['www', 'xz', 'zb']  # 只发布到这些站点
)
```

### 6. 缓存管理

```python
from sites import print_cache_status, clear_cache

# 查看缓存状态
print_cache_status()

# 清除所有缓存
clear_cache()
```

### 7. 查看所有站点分类

```python
from sites import print_all_categories

# 打印所有站点的分类信息
print_all_categories()

# 强制刷新并打印
print_all_categories(force_refresh=True)
```

## 高级用法

### 使用站点管理器类

```python
from sites import site_manager

# 直接使用管理器实例
categories = site_manager.get_site_categories('www')

# 获取缓存信息
cache_info = site_manager.get_cache_info()
print(f"缓存的站点: {cache_info['cached_sites']}")
print(f"总缓存分类数: {cache_info['total_cached_categories']}")

# 获取或创建分类
category_id = site_manager.get_or_create_category('www', '新分类名称')
```

### 批量操作示例

```python
from sites import get_all_sites_categories, publish_article

# 获取所有站点的分类信息
all_categories = get_all_sites_categories()

# 统计每个分类在多少个站点中存在
category_counts = {}
for site_key, categories in all_categories.items():
    for category_name in categories.keys():
        category_counts[category_name] = category_counts.get(category_name, 0) + 1

# 找出存在于最多站点的分类
most_common = max(category_counts.items(), key=lambda x: x[1])
print(f"最常见的分类: {most_common[0]} (存在于 {most_common[1]} 个站点)")

# 批量发布多篇文章
articles = [
    {"title": "文章1", "content": "内容1", "category": "分类1"},
    {"title": "文章2", "content": "内容2", "category": "分类2"},
]

for article in articles:
    results = publish_article(
        title=article["title"],
        content=article["content"], 
        category_name=article["category"]
    )
    success_count = sum(1 for r in results.values() if r["success"])
    print(f"文章 '{article['title']}' 发布到 {success_count} 个站点")
```

## 错误处理

```python
from sites import publish_article
import logging

# 启用日志
logging.basicConfig(level=logging.INFO)

try:
    results = publish_article(
        title="测试文章",
        content="测试内容",
        category_name="不存在的分类"
    )
    
    if not results:
        print("没有找到包含该分类的站点")
    else:
        for site_key, result in results.items():
            if not result['success']:
                print(f"站点 {site_key} 发布失败: {result['error']}")
                
except Exception as e:
    print(f"发布过程中出现错误: {e}")
```

## 配置说明

### 站点配置
站点配置在 `sites.py` 中的 `sites` 字典：

```python
sites = {
    'www': "https://yi958.com",
    'xz': "https://xz.yi958.com", 
    # ... 更多站点
}
```

### 缓存配置
- **缓存有效期**: 24小时（可在 `WordPressSiteManager.__init__` 中修改）
- **线程安全**: 使用线程锁确保缓存操作的安全性
- **自动过期**: 缓存会自动过期并重新加载

### WordPress API 配置
- **认证**: 使用用户名和API密钥
- **SSL验证**: 默认关闭（`verify_ssl: False`）
- **超时设置**: 60秒
- **重试机制**: 最多5次重试，每次延迟3秒

## 测试

运行测试脚本：

```bash
python test_sites.py
```

测试包括：
1. 基本功能测试
2. 模拟发布测试  
3. 交互式测试

## 注意事项

1. **首次使用**: 第一次获取分类时会从API加载，可能需要一些时间
2. **缓存更新**: 如果在WordPress后台添加了新分类，可以使用 `force_refresh=True` 强制刷新
3. **网络异常**: 内置重试机制会自动处理临时的网络问题
4. **并发安全**: 缓存操作是线程安全的，可以在多线程环境中使用
5. **向后兼容**: 保留了原有的 `get_category_id` 函数，但建议使用新的方法

## 性能优化

- ✅ 分类信息缓存24小时，避免重复API调用
- ✅ 线程安全的缓存机制
- ✅ 批量操作支持
- ✅ 智能重试机制
- ✅ 延迟加载，只在需要时获取分类信息
