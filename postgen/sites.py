#!/usr/bin/env python3
"""
WordPress 站点管理器
提供站点和分类管理、缓存机制、一键发布功能
"""

import requests
import logging
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta
import threading
from retry_decorator import wordpress_retry

# 配置日志
logger = logging.getLogger(__name__)

# WordPress API 配置
_WP_API_KEY = "Z6oB UFjS 25Lp LkZW yg9p dI9O"
_WP_USER = "admin"

# WordPress sites，全部都适用上面的_WP_API_KEY
sites = {
    'www': "https://yi958.com",
    'xz': "https://xz.yi958.com",
    'zb': "https://zb.yi958.com",
    'bz': "https://bz.yi958.com",
    'xm': "https://xm.yi958.com",
    'ziwei': "https://ziwei.yi958.com",
    'sxmx': "https://sxmx.yi958.com",
    'qimen': "https://qimen.yi958.com",
    'ms': "https://ms.yi958.com",
    'xh': "https://xh.yi958.com",
    'fs': "https://fs.yi958.com",
    'jm': "https://jm.yi958.com",
}


class WordPressSiteManager:
    """WordPress站点管理器，提供缓存和一键发布功能"""

    def __init__(self):
        # 分类缓存：{site_key: {category_name: category_id}}
        self._category_cache: Dict[str, Dict[str, int]] = {}
        # 缓存时间戳：{site_key: timestamp}
        self._cache_timestamps: Dict[str, datetime] = {}
        # 缓存有效期（小时）
        self._cache_duration = 24
        # 线程锁，确保缓存操作的线程安全
        self._cache_lock = threading.Lock()

        # WordPress配置
        self.wp_config = {
            "timeout": 60,
            "verify_ssl": False,
        }

    def _get_auth(self) -> Tuple[str, str]:
        """获取WordPress认证信息"""
        return (_WP_USER, _WP_API_KEY.replace(' ', ''))

    def _is_cache_valid(self, site_key: str) -> bool:
        """检查缓存是否有效"""
        if site_key not in self._cache_timestamps:
            return False

        cache_time = self._cache_timestamps[site_key]
        expiry_time = cache_time + timedelta(hours=self._cache_duration)
        return datetime.now() < expiry_time

    def _clear_site_cache(self, site_key: str):
        """清除指定站点的缓存"""
        with self._cache_lock:
            if site_key in self._category_cache:
                del self._category_cache[site_key]
            if site_key in self._cache_timestamps:
                del self._cache_timestamps[site_key]

    def clear_all_cache(self):
        """清除所有缓存"""
        with self._cache_lock:
            self._category_cache.clear()
            self._cache_timestamps.clear()
        logger.info("🗑️ 已清除所有站点分类缓存")

    @wordpress_retry(max_attempts=5, delay=3.0)
    def _fetch_categories_from_api(self, site_url: str) -> List[Dict[str, Any]]:
        """从API获取分类列表"""
        categories_url = f"{site_url}/wp-json/wp/v2/categories"

        response = requests.get(
            categories_url,
            verify=self.wp_config["verify_ssl"],
            timeout=self.wp_config["timeout"]
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"获取分类失败: {response.status_code} - {response.text}")

    def _load_site_categories(self, site_key: str) -> Dict[str, int]:
        """加载指定站点的所有分类到缓存"""
        if site_key not in sites:
            raise ValueError(f"未知的站点键: {site_key}")

        site_url = sites[site_key]
        logger.info(f"📂 正在加载站点 {site_key} 的分类...")

        try:
            categories = self._fetch_categories_from_api(site_url)

            # 构建分类名称到ID的映射
            category_map = {}
            for category in categories:
                category_map[category['name']] = category['id']

            # 更新缓存
            with self._cache_lock:
                self._category_cache[site_key] = category_map
                self._cache_timestamps[site_key] = datetime.now()

            logger.info(f"✅ 站点 {site_key} 加载了 {len(category_map)} 个分类到缓存")
            return category_map

        except Exception as e:
            logger.error(f"❌ 加载站点 {site_key} 分类失败: {e}")
            raise

    def get_site_categories(self, site_key: str, force_refresh: bool = False) -> Dict[str, int]:
        """
        获取指定站点的所有分类

        Args:
            site_key: 站点键名
            force_refresh: 是否强制刷新缓存

        Returns:
            Dict[str, int]: 分类名称到ID的映射
        """
        if site_key not in sites:
            raise ValueError(f"未知的站点键: {site_key}")

        # 检查缓存
        if not force_refresh and self._is_cache_valid(site_key) and site_key in self._category_cache:
            logger.debug(f"🎯 使用缓存的分类数据: {site_key}")
            return self._category_cache[site_key].copy()

        # 缓存无效或强制刷新，重新加载
        return self._load_site_categories(site_key)

    def get_all_sites_categories(self, force_refresh: bool = False) -> Dict[str, Dict[str, int]]:
        """
        获取所有站点的分类信息

        Args:
            force_refresh: 是否强制刷新缓存

        Returns:
            Dict[str, Dict[str, int]]: {site_key: {category_name: category_id}}
        """
        result = {}

        for site_key in sites.keys():
            try:
                result[site_key] = self.get_site_categories(site_key, force_refresh)
            except Exception as e:
                logger.error(f"❌ 获取站点 {site_key} 分类失败: {e}")
                result[site_key] = {}

        return result

    def find_category_in_sites(self, category_name: str, force_refresh: bool = False) -> List[Tuple[str, str, int]]:
        """
        在所有站点中查找指定分类

        Args:
            category_name: 分类名称
            force_refresh: 是否强制刷新缓存

        Returns:
            List[Tuple[str, str, int]]: [(site_key, site_url, category_id), ...]
        """
        results = []

        for site_key in sites.keys():
            try:
                categories = self.get_site_categories(site_key, force_refresh)
                if category_name in categories:
                    category_id = categories[category_name]
                    site_url = sites[site_key]
                    results.append((site_key, site_url, category_id))
                    logger.info(f"🎯 在站点 {site_key} 找到分类 '{category_name}' (ID: {category_id})")
            except Exception as e:
                logger.error(f"❌ 在站点 {site_key} 查找分类失败: {e}")

        if not results:
            logger.warning(f"⚠️ 在所有站点中都未找到分类: {category_name}")

        return results

    @wordpress_retry(max_attempts=5, delay=3.0)
    def _create_category(self, site_url: str, category_name: str) -> int:
        """在指定站点创建分类"""
        categories_url = f"{site_url}/wp-json/wp/v2/categories"

        create_data = {
            'name': category_name,
            'description': f'{category_name}相关文章'
        }

        response = requests.post(
            categories_url,
            json=create_data,
            auth=self._get_auth(),
            verify=self.wp_config["verify_ssl"],
            timeout=self.wp_config["timeout"]
        )

        if response.status_code == 201:
            new_category = response.json()
            return new_category['id']
        else:
            raise Exception(f"创建分类失败: {response.status_code} - {response.text}")

    def get_or_create_category(self, site_key: str, category_name: str) -> int:
        """
        获取或创建分类ID

        Args:
            site_key: 站点键名
            category_name: 分类名称

        Returns:
            int: 分类ID
        """
        if site_key not in sites:
            raise ValueError(f"未知的站点键: {site_key}")

        # 先尝试从缓存获取
        categories = self.get_site_categories(site_key)
        if category_name in categories:
            category_id = categories[category_name]
            logger.info(f"✅ 从缓存获取分类 '{category_name}' (ID: {category_id})")
            return category_id

        # 分类不存在，创建新分类
        site_url = sites[site_key]
        logger.info(f"📝 分类 '{category_name}' 不存在，正在创建...")

        try:
            category_id = self._create_category(site_url, category_name)

            # 更新缓存
            with self._cache_lock:
                if site_key not in self._category_cache:
                    self._category_cache[site_key] = {}
                self._category_cache[site_key][category_name] = category_id

            logger.info(f"✅ 成功创建分类 '{category_name}' (ID: {category_id})")
            return category_id

        except Exception as e:
            logger.error(f"❌ 创建分类失败: {e}")
            raise

    @wordpress_retry(max_attempts=5, delay=3.0)
    def _publish_post(self, site_url: str, title: str, content: str, category_id: int) -> Tuple[int, str]:
        """发布文章到WordPress"""
        posts_url = f"{site_url}/wp-json/wp/v2/posts"

        post_data = {
            'title': title,
            'content': content,
            'status': 'publish',
            'categories': [category_id] if category_id else []
        }

        response = requests.post(
            posts_url,
            json=post_data,
            auth=self._get_auth(),
            verify=self.wp_config["verify_ssl"],
            timeout=self.wp_config["timeout"]
        )

        if response.status_code == 201:
            post_info = response.json()
            return post_info['id'], post_info['link']
        else:
            raise Exception(f"发布失败: {response.status_code} - {response.text}")

    def publish_article(self, title: str, content: str, category_name: str,
                       target_sites: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        一键发布文章到指定分类的站点

        Args:
            title: 文章标题
            content: 文章内容
            category_name: 分类名称
            target_sites: 目标站点列表，如果为None则发布到所有有该分类的站点

        Returns:
            Dict[str, Dict[str, Any]]: 发布结果 {site_key: {"success": bool, "post_id": int, "post_url": str, "error": str}}
        """
        logger.info(f"📝 开始一键发布文章: {title[:50]}...")
        logger.info(f"🎯 目标分类: {category_name}")

        results = {}

        # 如果指定了目标站点，只在这些站点中查找
        if target_sites:
            sites_to_check = {k: v for k, v in sites.items() if k in target_sites}
        else:
            sites_to_check = sites

        # 查找有该分类的站点
        found_sites = []
        for site_key in sites_to_check.keys():
            try:
                categories = self.get_site_categories(site_key)
                if category_name in categories:
                    category_id = categories[category_name]
                    found_sites.append((site_key, sites[site_key], category_id))
                    logger.info(f"🎯 站点 {site_key} 有分类 '{category_name}' (ID: {category_id})")
            except Exception as e:
                logger.error(f"❌ 检查站点 {site_key} 分类失败: {e}")
                results[site_key] = {
                    "success": False,
                    "error": f"检查分类失败: {e}",
                    "post_id": None,
                    "post_url": None
                }

        if not found_sites:
            logger.warning(f"⚠️ 没有找到包含分类 '{category_name}' 的站点")
            return results

        # 发布到找到的站点
        for site_key, site_url, category_id in found_sites:
            try:
                logger.info(f"📤 正在发布到站点 {site_key}...")
                post_id, post_url = self._publish_post(site_url, title, content, category_id)

                results[site_key] = {
                    "success": True,
                    "post_id": post_id,
                    "post_url": post_url,
                    "error": None
                }

                logger.info(f"✅ 成功发布到 {site_key} - ID: {post_id}")

            except Exception as e:
                logger.error(f"❌ 发布到站点 {site_key} 失败: {e}")
                results[site_key] = {
                    "success": False,
                    "error": str(e),
                    "post_id": None,
                    "post_url": None
                }

        # 统计结果
        success_count = sum(1 for r in results.values() if r["success"])
        total_count = len(results)

        logger.info(f"📊 发布完成: {success_count}/{total_count} 个站点成功")

        return results

    def publish_to_specific_site(self, site_key: str, title: str, content: str, category_name: str) -> Dict[str, Any]:
        """
        发布文章到指定站点的指定分类

        Args:
            site_key: 站点键名
            title: 文章标题
            content: 文章内容
            category_name: 分类名称

        Returns:
            Dict[str, Any]: 发布结果
        """
        if site_key not in sites:
            raise ValueError(f"未知的站点键: {site_key}")

        logger.info(f"📝 发布文章到站点 {site_key}: {title[:50]}...")

        try:
            # 获取或创建分类
            category_id = self.get_or_create_category(site_key, category_name)

            # 发布文章
            site_url = sites[site_key]
            post_id, post_url = self._publish_post(site_url, title, content, category_id)

            result = {
                "success": True,
                "post_id": post_id,
                "post_url": post_url,
                "category_id": category_id,
                "error": None
            }

            logger.info(f"✅ 成功发布到 {site_key} - ID: {post_id}")
            return result

        except Exception as e:
            logger.error(f"❌ 发布到站点 {site_key} 失败: {e}")
            return {
                "success": False,
                "post_id": None,
                "post_url": None,
                "category_id": None,
                "error": str(e)
            }

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            cache_info = {
                "cached_sites": list(self._category_cache.keys()),
                "cache_timestamps": {
                    site: timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    for site, timestamp in self._cache_timestamps.items()
                },
                "total_cached_categories": sum(
                    len(categories) for categories in self._category_cache.values()
                ),
                "cache_duration_hours": self._cache_duration
            }

        return cache_info

    def print_cache_status(self):
        """打印缓存状态"""
        cache_info = self.get_cache_info()

        print("🗂️ 分类缓存状态")
        print("=" * 50)
        print(f"缓存有效期: {cache_info['cache_duration_hours']} 小时")
        print(f"已缓存站点数: {len(cache_info['cached_sites'])}")
        print(f"总缓存分类数: {cache_info['total_cached_categories']}")

        if cache_info['cached_sites']:
            print("\n📋 各站点缓存详情:")
            for site_key in cache_info['cached_sites']:
                categories_count = len(self._category_cache[site_key])
                cache_time = cache_info['cache_timestamps'][site_key]
                is_valid = "✅" if self._is_cache_valid(site_key) else "❌"
                print(f"  {site_key}: {categories_count} 个分类, 缓存时间: {cache_time} {is_valid}")
        else:
            print("\n📭 暂无缓存数据")

    def print_all_categories(self, force_refresh: bool = False):
        """打印所有站点的分类信息"""
        print("📂 所有站点分类信息")
        print("=" * 60)

        all_categories = self.get_all_sites_categories(force_refresh)

        for site_key, categories in all_categories.items():
            site_url = sites[site_key]
            print(f"\n🌐 站点: {site_key} ({site_url})")

            if categories:
                print(f"   分类数量: {len(categories)}")
                for name, cat_id in sorted(categories.items()):
                    print(f"   - {name} (ID: {cat_id})")
            else:
                print("   ❌ 无法获取分类信息")


# 全局站点管理器实例
site_manager = WordPressSiteManager()


# 便捷函数
def get_site_categories(site_key: str, force_refresh: bool = False) -> Dict[str, int]:
    """获取指定站点的所有分类"""
    return site_manager.get_site_categories(site_key, force_refresh)


def get_all_sites_categories(force_refresh: bool = False) -> Dict[str, Dict[str, int]]:
    """获取所有站点的分类信息"""
    return site_manager.get_all_sites_categories(force_refresh)


def find_category_in_sites(category_name: str, force_refresh: bool = False) -> List[Tuple[str, str, int]]:
    """在所有站点中查找指定分类"""
    return site_manager.find_category_in_sites(category_name, force_refresh)


def publish_article(title: str, content: str, category_name: str,
                   target_sites: List[str] = None) -> Dict[str, Dict[str, Any]]:
    """一键发布文章到指定分类的站点"""
    return site_manager.publish_article(title, content, category_name, target_sites)


def publish_to_specific_site(site_key: str, title: str, content: str, category_name: str) -> Dict[str, Any]:
    """发布文章到指定站点的指定分类"""
    return site_manager.publish_to_specific_site(site_key, title, content, category_name)


def clear_cache():
    """清除所有缓存"""
    site_manager.clear_all_cache()


def print_cache_status():
    """打印缓存状态"""
    site_manager.print_cache_status()


def print_all_categories(force_refresh: bool = False):
    """打印所有站点的分类信息"""
    site_manager.print_all_categories(force_refresh)


# 向后兼容的函数
def get_category_id(site_url: str, category_name: str) -> int:
    """
    向后兼容函数：获取分类ID
    注意：这个函数不使用缓存，建议使用新的方法
    """
    # 找到对应的site_key
    site_key = None
    for key, url in sites.items():
        if url == site_url:
            site_key = key
            break

    if not site_key:
        raise ValueError(f"未找到对应的站点: {site_url}")

    return site_manager.get_or_create_category(site_key, category_name)


if __name__ == "__main__":
    # 测试代码
    print("🧪 WordPress站点管理器测试")
    print("=" * 50)

    try:
        # 测试获取分类
        print("\n1. 测试获取站点分类...")
        categories = get_site_categories('www')
        print(f"主站点分类数量: {len(categories)}")

        # 测试查找分类
        print("\n2. 测试查找分类...")
        results = find_category_in_sites('生肖今日运势')
        print(f"找到 {len(results)} 个站点包含该分类")

        # 打印缓存状态
        print("\n3. 缓存状态:")
        print_cache_status()

    except Exception as e:
        print(f"❌ 测试失败: {e}")

    def publish_to_specific_site(self, site_key: str, title: str, content: str, category_name: str) -> Dict[str, Any]:
        """
        发布文章到指定站点的指定分类

        Args:
            site_key: 站点键名
            title: 文章标题
            content: 文章内容
            category_name: 分类名称

        Returns:
            Dict[str, Any]: 发布结果
        """
        if site_key not in sites:
            raise ValueError(f"未知的站点键: {site_key}")

        logger.info(f"📝 发布文章到站点 {site_key}: {title[:50]}...")

        try:
            # 获取或创建分类
            category_id = self.get_or_create_category(site_key, category_name)

            # 发布文章
            site_url = sites[site_key]
            post_id, post_url = self._publish_post(site_url, title, content, category_id)

            result = {
                "success": True,
                "post_id": post_id,
                "post_url": post_url,
                "category_id": category_id,
                "error": None
            }

            logger.info(f"✅ 成功发布到 {site_key} - ID: {post_id}")
            return result

        except Exception as e:
            logger.error(f"❌ 发布到站点 {site_key} 失败: {e}")
            return {
                "success": False,
                "post_id": None,
                "post_url": None,
                "category_id": None,
                "error": str(e)
            }

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            cache_info = {
                "cached_sites": list(self._category_cache.keys()),
                "cache_timestamps": {
                    site: timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    for site, timestamp in self._cache_timestamps.items()
                },
                "total_cached_categories": sum(
                    len(categories) for categories in self._category_cache.values()
                ),
                "cache_duration_hours": self._cache_duration
            }

        return cache_info

    def print_cache_status(self):
        """打印缓存状态"""
        cache_info = self.get_cache_info()

        print("🗂️ 分类缓存状态")
        print("=" * 50)
        print(f"缓存有效期: {cache_info['cache_duration_hours']} 小时")
        print(f"已缓存站点数: {len(cache_info['cached_sites'])}")
        print(f"总缓存分类数: {cache_info['total_cached_categories']}")

        if cache_info['cached_sites']:
            print("\n📋 各站点缓存详情:")
            for site_key in cache_info['cached_sites']:
                categories_count = len(self._category_cache[site_key])
                cache_time = cache_info['cache_timestamps'][site_key]
                is_valid = "✅" if self._is_cache_valid(site_key) else "❌"
                print(f"  {site_key}: {categories_count} 个分类, 缓存时间: {cache_time} {is_valid}")
        else:
            print("\n📭 暂无缓存数据")

    def print_all_categories(self, force_refresh: bool = False):
        """打印所有站点的分类信息"""
        print("📂 所有站点分类信息")
        print("=" * 60)

        all_categories = self.get_all_sites_categories(force_refresh)

        for site_key, categories in all_categories.items():
            site_url = sites[site_key]
            print(f"\n🌐 站点: {site_key} ({site_url})")

            if categories:
                print(f"   分类数量: {len(categories)}")
                for name, cat_id in sorted(categories.items()):
                    print(f"   - {name} (ID: {cat_id})")
            else:
                print("   ❌ 无法获取分类信息")


# 全局站点管理器实例
site_manager = WordPressSiteManager()


# 便捷函数
def get_site_categories(site_key: str, force_refresh: bool = False) -> Dict[str, int]:
    """获取指定站点的所有分类"""
    return site_manager.get_site_categories(site_key, force_refresh)


def get_all_sites_categories(force_refresh: bool = False) -> Dict[str, Dict[str, int]]:
    """获取所有站点的分类信息"""
    return site_manager.get_all_sites_categories(force_refresh)


def find_category_in_sites(category_name: str, force_refresh: bool = False) -> List[Tuple[str, str, int]]:
    """在所有站点中查找指定分类"""
    return site_manager.find_category_in_sites(category_name, force_refresh)


def publish_article(title: str, content: str, category_name: str,
                   target_sites: Optional[List[str]] = None) -> Dict[str, Dict[str, Any]]:
    """一键发布文章到指定分类的站点"""
    return site_manager.publish_article(title, content, category_name, target_sites)


def publish_to_specific_site(site_key: str, title: str, content: str, category_name: str) -> Dict[str, Any]:
    """发布文章到指定站点的指定分类"""
    return site_manager.publish_to_specific_site(site_key, title, content, category_name)


def clear_cache():
    """清除所有缓存"""
    site_manager.clear_all_cache()


def print_cache_status():
    """打印缓存状态"""
    site_manager.print_cache_status()


def print_all_categories(force_refresh: bool = False):
    """打印所有站点的分类信息"""
    site_manager.print_all_categories(force_refresh)


# 向后兼容的函数
def get_category_id(site_url: str, category_name: str) -> int:
    """
    向后兼容函数：获取分类ID
    注意：这个函数不使用缓存，建议使用新的方法
    """
    # 找到对应的site_key
    site_key = None
    for key, url in sites.items():
        if url == site_url:
            site_key = key
            break

    if not site_key:
        raise ValueError(f"未找到对应的站点: {site_url}")

    return site_manager.get_or_create_category(site_key, category_name)


if __name__ == "__main__":
    # 测试代码
    print("🧪 WordPress站点管理器测试")
    print("=" * 50)

    try:
        # 测试获取分类
        print("\n1. 测试获取站点分类...")
        categories = get_site_categories('www')
        print(f"主站点分类数量: {len(categories)}")

        # 测试查找分类
        print("\n2. 测试查找分类...")
        results = find_category_in_sites('生肖今日运势')
        print(f"找到 {len(results)} 个站点包含该分类")

        # 打印缓存状态
        print("\n3. 缓存状态:")
        print_cache_status()

    except Exception as e:
        print(f"❌ 测试失败: {e}")
