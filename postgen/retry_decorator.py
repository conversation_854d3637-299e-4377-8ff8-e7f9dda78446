#!/usr/bin/env python3
"""
重试装饰器模块
提供网络请求和API调用的重试机制
"""

import time
import functools
import logging
from typing import Callable, Any, Tuple, Type, Union

logger = logging.getLogger(__name__)

def retry(
    max_attempts: int = 5,
    delay: float = 2.0,
    backoff: float = 2.0,
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    on_retry: Callable[[int, Exception], None] = None
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数（包括首次尝试）
        delay: 初始延迟时间（秒）
        backoff: 延迟时间倍数（指数退避）
        exceptions: 需要重试的异常类型
        on_retry: 重试时的回调函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            current_delay = delay
            last_exception = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    result = func(*args, **kwargs)
                    if attempt > 1:
                        logger.info(f"✅ {func.__name__} 在第 {attempt} 次尝试后成功")
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts:
                        logger.error(f"❌ {func.__name__} 在 {max_attempts} 次尝试后仍然失败: {e}")
                        raise e
                    
                    logger.warning(f"⚠️ {func.__name__} 第 {attempt} 次尝试失败: {e}")
                    logger.info(f"🔄 将在 {current_delay:.1f} 秒后进行第 {attempt + 1} 次尝试...")
                    
                    # 调用重试回调
                    if on_retry:
                        try:
                            on_retry(attempt, e)
                        except Exception as callback_error:
                            logger.warning(f"重试回调函数出错: {callback_error}")
                    
                    # 等待后重试
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            # 理论上不会到达这里，但为了类型安全
            raise last_exception
        
        return wrapper
    return decorator

def network_retry(max_attempts: int = 5, delay: float = 2.0):
    """
    网络请求专用重试装饰器
    针对常见的网络异常进行重试
    """
    import requests
    from requests.exceptions import (
        ConnectionError, Timeout, HTTPError, 
        RequestException, SSLError
    )
    from urllib3.exceptions import SSLError as Urllib3SSLError
    
    network_exceptions = (
        ConnectionError,
        Timeout, 
        HTTPError,
        RequestException,
        SSLError,
        Urllib3SSLError,
        OSError,  # 包含SSL相关的OSError
    )
    
    def on_network_retry(attempt: int, exception: Exception):
        """网络重试回调"""
        logger.info(f"🌐 网络请求重试 - 尝试 {attempt}, 异常: {type(exception).__name__}")
    
    return retry(
        max_attempts=max_attempts,
        delay=delay,
        backoff=1.5,  # 网络请求使用较小的退避倍数
        exceptions=network_exceptions,
        on_retry=on_network_retry
    )

def openai_retry(max_attempts: int = 3, delay: float = 1.0):
    """
    OpenAI API专用重试装饰器
    """
    from openai import OpenAIError, RateLimitError, APITimeoutError
    
    openai_exceptions = (
        OpenAIError,
        RateLimitError, 
        APITimeoutError,
        ConnectionError,
        OSError,
    )
    
    def on_openai_retry(attempt: int, exception: Exception):
        """OpenAI重试回调"""
        if "rate limit" in str(exception).lower():
            logger.info(f"🤖 OpenAI API 速率限制 - 尝试 {attempt}")
        else:
            logger.info(f"🤖 OpenAI API 重试 - 尝试 {attempt}, 异常: {type(exception).__name__}")
    
    return retry(
        max_attempts=max_attempts,
        delay=delay,
        backoff=2.0,
        exceptions=openai_exceptions,
        on_retry=on_openai_retry
    )

def wordpress_retry(max_attempts: int = 5, delay: float = 3.0):
    """
    WordPress API专用重试装饰器
    """
    import requests
    from requests.exceptions import (
        ConnectionError, Timeout, HTTPError, 
        RequestException, SSLError
    )
    from urllib3.exceptions import SSLError as Urllib3SSLError
    
    wordpress_exceptions = (
        ConnectionError,
        Timeout,
        HTTPError, 
        RequestException,
        SSLError,
        Urllib3SSLError,
        OSError,
    )
    
    def on_wordpress_retry(attempt: int, exception: Exception):
        """WordPress重试回调"""
        if "ssl" in str(exception).lower():
            logger.info(f"🔒 WordPress SSL 重试 - 尝试 {attempt}")
        else:
            logger.info(f"📝 WordPress API 重试 - 尝试 {attempt}, 异常: {type(exception).__name__}")
    
    return retry(
        max_attempts=max_attempts,
        delay=delay,
        backoff=1.5,
        exceptions=wordpress_exceptions,
        on_retry=on_wordpress_retry
    )

# 便捷的重试函数
def retry_on_failure(func: Callable, max_attempts: int = 5, delay: float = 2.0, **kwargs) -> Any:
    """
    直接对函数进行重试调用
    
    Args:
        func: 要重试的函数
        max_attempts: 最大重试次数
        delay: 延迟时间
        **kwargs: 传递给函数的参数
    """
    @retry(max_attempts=max_attempts, delay=delay)
    def wrapper():
        return func(**kwargs)
    
    return wrapper()

# 示例使用
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    @network_retry(max_attempts=3, delay=1.0)
    def test_network_request():
        import requests
        response = requests.get("https://httpbin.org/status/500")
        response.raise_for_status()
        return response.json()
    
    try:
        result = test_network_request()
        print(f"请求成功: {result}")
    except Exception as e:
        print(f"请求最终失败: {e}")
