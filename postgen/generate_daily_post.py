#!/usr/bin/env python3
"""
生肖今日运势自动生成器
自动生成12生肖的今日运势文章并发布到WordPress站点
"""

import os
import json
import requests
import logging
from datetime import datetime
from config import get_config, get_openai_client
from retry_decorator import openai_retry, wordpress_retry, network_retry

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取配置实例
config = get_config()

# 获取OpenAI客户端
client = get_openai_client()

# 获取生肖列表
ZODIAC_ANIMALS = config.get_zodiac_animals()

# 获取重试配置
retry_config = config.get_retry_config()

@openai_retry(
    max_attempts=retry_config["openai_max_attempts"],
    delay=retry_config["openai_delay"]
)
def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    logger.info(f"🤖 开始生成{zodiac}运势内容...")

    # 获取提示词模板
    prompt_template = config.get_prompt_template()
    prompt = prompt_template.format(date=date_str, zodiac=zodiac)

    # 获取OpenAI配置
    openai_config = config.get_openai_config()

    response = client.chat.completions.create(
        model=openai_config["model"],
        messages=[
            {"role": "user", "content": prompt}
        ],
        temperature=openai_config["temperature"],
        max_tokens=openai_config["max_tokens"]
    )

    # 获取生成的正文内容
    content = response.choices[0].message.content.strip()

    # 使用配置中的模板格式化完整内容
    full_content = config.format_article_content(zodiac, date_str, content)

    logger.info(f"✅ {zodiac}运势内容生成成功")
    return full_content

@wordpress_retry(
    max_attempts=retry_config["wordpress_max_attempts"],
    delay=retry_config["wordpress_delay"]
)
def get_category_id(site_url, category_name):
    """
    获取WordPress分类ID，如果不存在则创建
    """
    logger.info(f"📂 获取WordPress分类: {category_name}")

    # 获取现有分类
    categories_url = f"{site_url}/wp-json/wp/v2/categories"

    # 获取WordPress配置
    wp_config = config.get_wordpress_config()
    auth = config.get_wordpress_auth()

    # 添加SSL验证和超时设置
    response = requests.get(
        categories_url,
        verify=wp_config["verify_ssl"],
        timeout=wp_config["timeout"]
    )

    if response.status_code == 200:
        categories = response.json()
        for category in categories:
            if category['name'] == category_name:
                logger.info(f"✅ 找到现有分类: {category_name} (ID: {category['id']})")
                return category['id']

    # 如果分类不存在，创建新分类
    logger.info(f"📝 分类 '{category_name}' 不存在，正在创建...")
    create_data = {
        'name': category_name,
        'description': f'{category_name}相关文章'
    }

    response = requests.post(
        categories_url,
        json=create_data,
        auth=auth,
        verify=wp_config["verify_ssl"],
        timeout=wp_config["timeout"]
    )

    if response.status_code == 201:
        new_category = response.json()
        logger.info(f"✅ 成功创建分类: {category_name} (ID: {new_category['id']})")
        return new_category['id']
    else:
        error_msg = f"创建分类失败: {response.status_code} - {response.text}"
        logger.error(error_msg)
        raise Exception(error_msg)

@wordpress_retry(
    max_attempts=retry_config["wordpress_max_attempts"],
    delay=retry_config["wordpress_delay"]
)
def publish_to_wordpress(site_url, title, content, category_id):
    """
    发布文章到WordPress
    """
    logger.info(f"📝 发布文章到WordPress: {title[:50]}...")

    posts_url = f"{site_url}/wp-json/wp/v2/posts"

    post_data = {
        'title': title,
        'content': content,
        'status': 'publish',
        'categories': [category_id] if category_id else []
    }

    # 获取WordPress配置和认证信息
    wp_config = config.get_wordpress_config()
    auth = config.get_wordpress_auth()

    # 添加SSL验证和超时设置
    response = requests.post(
        posts_url,
        json=post_data,
        auth=auth,
        verify=wp_config["verify_ssl"],
        timeout=wp_config["timeout"]
    )

    if response.status_code == 201:
        post_info = response.json()
        logger.info(f"✅ 文章发布成功: ID {post_info['id']}")
        return post_info['id'], post_info['link']
    else:
        error_msg = f"发布失败: {response.status_code} - {response.text}"
        logger.error(error_msg)
        raise Exception(error_msg)

def main():
    """
    主函数：生成并发布12生肖今日运势
    """
    import sys

    # 获取今日日期
    today = datetime.now()
    date_str = today.strftime("%Y年%m月%d日")

    # 检查命令行参数
    target_zodiacs = ZODIAC_ANIMALS
    if len(sys.argv) > 1:
        specified_zodiac = sys.argv[1]
        if specified_zodiac in ZODIAC_ANIMALS:
            target_zodiacs = [specified_zodiac]
            print(f"只生成指定生肖：{specified_zodiac}")
        else:
            print(f"错误：未知的生肖 '{specified_zodiac}'")
            print(f"可用的生肖：{', '.join(ZODIAC_ANIMALS)}")
            return

    print(f"开始生成{date_str}的生肖运势...")
    print(f"目标生肖：{', '.join(target_zodiacs)}")

    # 获取www站点URL
    www_site = config.get_wordpress_site_url('www')
    if not www_site:
        print("错误：未找到www站点配置")
        return

    print(f"目标站点：{www_site}")

    # 获取或创建"生肖今日运势"分类
    wp_config = config.get_wordpress_config()
    category_id = get_category_id(www_site, wp_config["category_name"])
    if not category_id:
        print("警告：无法获取分类ID，将不设置分类")

    success_count = 0

    # 为每个生肖生成并发布文章
    for zodiac in target_zodiacs:
        logger.info(f"\n🐲 正在处理生肖：{zodiac}")

        try:
            # 生成运势内容
            content = generate_fortune_content(date_str, zodiac)

            # 提取标题（假设第一行是标题）
            lines = content.split('\n')
            title = lines[0].strip('「」')

            # 发布到WordPress
            post_id, post_url = publish_to_wordpress(www_site, title, content, category_id)

            logger.info(f"✅ {zodiac}运势处理完成")
            logger.info(f"   文章ID: {post_id}")
            logger.info(f"   文章链接: {post_url}")
            success_count += 1

        except Exception as e:
            logger.error(f"❌ {zodiac}运势处理失败: {e}")
            # 继续处理下一个生肖，不中断整个流程

    print(f"\n任务完成！成功发布 {success_count}/{len(target_zodiacs)} 篇文章")

if __name__ == "__main__":
    print("脚本开始执行...")
    try:
        main()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()
